/// <reference path="../types/express.d.ts" />
import { Request, Response, NextFunction } from 'express';
import { UserRole } from '@prisma/client';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import {
  createSuccessResponse,
  createPaginatedResponse,
  validatePaginationParams,
  calculateSkip,
} from '../viewmodels/responseViewModel';
import { toUserDirectoryViewModel } from '../viewmodels/userProfileViewModel';

/**
 * Get User Directory
 * Returns paginated list of users with search and filtering capabilities
 */
export const getUserDirectory = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const tenantId = req.user?.tenant_id;

    if (!tenantId) {
      throw createError('User not authenticated', 401);
    }

    const { page, limit } = validatePaginationParams(
      req.query.page as string,
      req.query.limit as string
    );
    const search = req.query.search as string;
    const role = req.query.role as UserRole;
    const course = req.query.course as string;
    const batch_year = req.query.batch_year as string;
    const company = req.query.company as string;
    const location = req.query.location as string;

    const skip = calculateSkip(page, limit);

    // Build where clause for filtering
    const whereClause: any = {
      tenant_id: tenantId,
      account_status: 'APPROVED',
      // Exclude admin users from directory
      role: {
        not: 'SUPER_ADMIN',
      },
    };

    // Add role filter
    if (role && role !== 'ALL') {
      whereClause.role = role;
    }

    // Add search functionality
    if (search) {
      whereClause.OR = [
        {
          full_name: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          email: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          profile: {
            company: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          profile: {
            job_title: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
      ];
    }

    // Add profile-based filters
    const profileFilters: any = {};

    if (course) {
      profileFilters.course = {
        course_name: {
          contains: course,
          mode: 'insensitive',
        },
      };
    }

    if (batch_year) {
      profileFilters.batch_year = parseInt(batch_year);
    }

    if (company) {
      profileFilters.company = {
        contains: company,
        mode: 'insensitive',
      };
    }

    if (location) {
      profileFilters.current_location = {
        contains: location,
        mode: 'insensitive',
      };
    }

    if (Object.keys(profileFilters).length > 0) {
      whereClause.profile = profileFilters;
    }

    // Fetch users with pagination
    const users = await prisma.user.findMany({
      where: whereClause,
      include: {
        profile: {
          include: {
            course: {
              select: {
                course_name: true,
              },
            },
          },
        },
      },
      skip,
      take: limit,
      orderBy: [
        { full_name: 'asc' },
        { created_at: 'desc' },
      ],
    });

    // Get total count for pagination
    const total = await prisma.user.count({
      where: whereClause,
    });

    // Apply view model to filter sensitive information
    const filteredUsers = users.map(user => toUserDirectoryViewModel(user));

    const response = createPaginatedResponse(filteredUsers, page, limit, total);
    res.json(response);
  } catch (error) {
    next(error);
  }
};

/**
 * Get User by ID
 * Returns detailed information about a specific user
 */
export const getUserById = async (
  req: AuthenticatedRequest<{ id: string }>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = parseInt(req.params.id);
    const currentUserId = req.user?.id;
    const tenantId = req.user?.tenant_id;

    if (!tenantId) {
      throw createError('User not authenticated', 401);
    }

    const user = await prisma.user.findFirst({
      where: {
        id: userId,
        tenant_id: tenantId,
        account_status: 'APPROVED',
      },
      include: {
        profile: {
          include: {
            course: {
              select: {
                course_name: true,
              },
            },
          },
        },
        _count: {
          select: {
            general_posts: true,
            jobs: true,
          },
        },
      },
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    // Check if current user is connected to this user for enhanced visibility
    let isConnected = false;
    if (currentUserId && currentUserId !== userId) {
      const connection = await prisma.follow.findFirst({
        where: {
          OR: [
            { follower_id: currentUserId, following_id: userId, status: 'ACCEPTED' },
            { follower_id: userId, following_id: currentUserId, status: 'ACCEPTED' },
          ],
        },
      });
      isConnected = !!connection;
    }

    // Apply view model to filter sensitive information
    // Show more info if it's the user's own profile or if they're connected
    const showSensitiveInfo = currentUserId === userId || isConnected;
    const filteredUser = toUserDirectoryViewModel(user);

    // Add connection status and additional info for connected users
    const responseData = {
      ...filteredUser,
      is_connected: isConnected,
      posts_count: user._count.general_posts,
      jobs_count: user._count.jobs,
    };

    const response = createSuccessResponse(responseData);
    res.json(response);
  } catch (error) {
    next(error);
  }
};

/**
 * Get Directory Statistics
 * Returns statistics about users in the directory
 */
export const getDirectoryStats = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const tenantId = req.user?.tenant_id;

    if (!tenantId) {
      throw createError('User not authenticated', 401);
    }

    // Get user counts by role
    const usersByRole = await prisma.user.groupBy({
      by: ['role'],
      where: {
        tenant_id: tenantId,
        account_status: 'APPROVED',
        role: {
          not: 'SUPER_ADMIN',
        },
      },
      _count: {
        id: true,
      },
    });

    // Get user counts by batch year
    const usersByBatchYear = await prisma.userProfile.groupBy({
      by: ['batch_year'],
      where: {
        tenant_id: tenantId,
        user: {
          account_status: 'APPROVED',
        },
        batch_year: {
          not: null,
        },
      },
      _count: {
        id: true,
      },
      orderBy: {
        batch_year: 'desc',
      },
      take: 10, // Last 10 batch years
    });

    // Get top courses
    const topCourses = await prisma.course.findMany({
      where: {
        tenant_id: tenantId,
      },
      include: {
        _count: {
          select: {
            profiles: {
              where: {
                user: {
                  account_status: 'APPROVED',
                },
              },
            },
          },
        },
      },
      orderBy: {
        profiles: {
          _count: 'desc',
        },
      },
      take: 10,
    });

    // Get top companies
    const topCompanies = await prisma.userProfile.groupBy({
      by: ['company'],
      where: {
        tenant_id: tenantId,
        company: {
          not: null,
        },
        user: {
          account_status: 'APPROVED',
        },
      },
      _count: {
        id: true,
      },
      orderBy: {
        _count: {
          id: 'desc',
        },
      },
      take: 10,
    });

    // Get top locations
    const topLocations = await prisma.userProfile.groupBy({
      by: ['current_location'],
      where: {
        tenant_id: tenantId,
        current_location: {
          not: null,
        },
        user: {
          account_status: 'APPROVED',
        },
      },
      _count: {
        id: true,
      },
      orderBy: {
        _count: {
          id: 'desc',
        },
      },
      take: 10,
    });

    const stats = {
      total_users: usersByRole.reduce((sum, role) => sum + role._count.id, 0),
      users_by_role: usersByRole.map(role => ({
        role: role.role,
        count: role._count.id,
      })),
      users_by_batch_year: usersByBatchYear.map(batch => ({
        batch_year: batch.batch_year,
        count: batch._count.id,
      })),
      top_courses: topCourses.map(course => ({
        course_name: course.course_name,
        count: course._count.profiles,
      })),
      top_companies: topCompanies.map(company => ({
        company: company.company,
        count: company._count.id,
      })),
      top_locations: topLocations.map(location => ({
        location: location.current_location,
        count: location._count.id,
      })),
    };

    const response = createSuccessResponse(stats);
    res.json(response);
  } catch (error) {
    next(error);
  }
};

/**
 * Search Users
 * Advanced search functionality with multiple filters
 */
export const searchUsers = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const tenantId = req.user?.tenant_id;

    if (!tenantId) {
      throw createError('User not authenticated', 401);
    }

    const { page, limit } = validatePaginationParams(
      req.query.page as string,
      req.query.limit as string
    );
    const query = req.query.q as string;

    if (!query || query.trim().length < 2) {
      throw createError('Search query must be at least 2 characters long', 400);
    }

    const skip = calculateSkip(page, limit);

    // Advanced search across multiple fields
    const users = await prisma.user.findMany({
      where: {
        tenant_id: tenantId,
        account_status: 'APPROVED',
        role: {
          not: 'SUPER_ADMIN',
        },
        OR: [
          {
            full_name: {
              contains: query,
              mode: 'insensitive',
            },
          },
          {
            email: {
              contains: query,
              mode: 'insensitive',
            },
          },
          {
            profile: {
              company: {
                contains: query,
                mode: 'insensitive',
              },
            },
          },
          {
            profile: {
              job_title: {
                contains: query,
                mode: 'insensitive',
              },
            },
          },
          {
            profile: {
              current_location: {
                contains: query,
                mode: 'insensitive',
              },
            },
          },
          {
            profile: {
              course: {
                course_name: {
                  contains: query,
                  mode: 'insensitive',
                },
              },
            },
          },
        ],
      },
      include: {
        profile: {
          include: {
            course: {
              select: {
                course_name: true,
              },
            },
          },
        },
      },
      skip,
      take: limit,
      orderBy: [
        { full_name: 'asc' },
      ],
    });

    const total = await prisma.user.count({
      where: {
        tenant_id: tenantId,
        account_status: 'APPROVED',
        role: {
          not: 'SUPER_ADMIN',
        },
        OR: [
          {
            full_name: {
              contains: query,
              mode: 'insensitive',
            },
          },
          {
            email: {
              contains: query,
              mode: 'insensitive',
            },
          },
          {
            profile: {
              company: {
                contains: query,
                mode: 'insensitive',
              },
            },
          },
          {
            profile: {
              job_title: {
                contains: query,
                mode: 'insensitive',
              },
            },
          },
          {
            profile: {
              current_location: {
                contains: query,
                mode: 'insensitive',
              },
            },
          },
          {
            profile: {
              course: {
                course_name: {
                  contains: query,
                  mode: 'insensitive',
                },
              },
            },
          },
        ],
      },
    });

    // Apply view model to filter sensitive information
    const filteredUsers = users.map(user => toUserDirectoryViewModel(user));

    const response = createPaginatedResponse(filteredUsers, page, limit, total);
    res.json(response);
  } catch (error) {
    next(error);
  }
};
