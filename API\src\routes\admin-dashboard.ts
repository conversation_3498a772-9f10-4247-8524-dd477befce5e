import express from 'express';
import { query } from 'express-validator';
import { validateRequest } from '../middleware/validateRequest';
import { authenticateToken, requireRole } from '../middleware/auth';
import * as AdminDashboardController from '../controllers/AdminDashboardController';

const router = express.Router();

/**
 * @swagger
 * /api/admin/dashboard:
 *   get:
 *     summary: Get admin dashboard statistics
 *     tags: [Admin - Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard statistics retrieved successfully
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/',
  authenticateToken,
  requireRole(['TENANT_ADMIN', 'SUPER_ADMIN']),
  AdminDashboardController.getDashboardStats
);

/**
 * @swagger
 * /api/admin/dashboard/user-activity:
 *   get:
 *     summary: Get user activity report
 *     tags: [Admin - Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for the report (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: User activity report retrieved successfully
 *       400:
 *         description: Invalid date format
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/user-activity',
  authenticateToken,
  requireRole(['TENANT_ADMIN', 'SUPER_ADMIN']),
  [
    query('start_date').optional().isISO8601().toDate(),
    query('end_date').optional().isISO8601().toDate(),
  ],
  validateRequest,
  AdminDashboardController.getUserActivityReport
);

/**
 * @swagger
 * /api/admin/dashboard/content-activity:
 *   get:
 *     summary: Get content activity report
 *     tags: [Admin - Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for the report (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: Content activity report retrieved successfully
 *       400:
 *         description: Invalid date format
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/content-activity',
  authenticateToken,
  requireRole(['TENANT_ADMIN', 'SUPER_ADMIN']),
  [
    query('start_date').optional().isISO8601().toDate(),
    query('end_date').optional().isISO8601().toDate(),
  ],
  validateRequest,
  AdminDashboardController.getContentActivityReport
);

export default router;
