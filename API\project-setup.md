# IonAlumni - Alumni Portal Platform

A comprehensive multi-tenant alumni portal platform built with modern technologies, featuring real-time communication, job postings, event management, and social networking capabilities for educational institutions.

## 🚀 Features

- **Multi-tenant Architecture**: Support for multiple educational institutions
- **User Management**: Role-based access control (Admin, Alumni, Students)
- **Real-time Communication**: Socket.IO powered messaging and notifications
- **Job Portal**: Job postings and applications management
- **Event Management**: Create and manage alumni events
- **Social Features**: Posts, follows, and networking capabilities
- **File Upload**: Profile pictures and document management
- **API Documentation**: Comprehensive Swagger/OpenAPI documentation
- **Security**: JWT authentication, rate limiting, and data validation

## 🏗️ Architecture

### Backend (API)
- **Framework**: Node.js with Express.js and TypeScript
- **Database**: MySQL with Prisma ORM
- **Authentication**: JWT with refresh tokens
- **Real-time**: Socket.IO for live updates
- **Caching**: Redis for session management and caching
- **File Storage**: Local file system with configurable paths
- **Documentation**: Swagger/OpenAPI 3.0
- **Code Quality**: ESLint, Prettier, and comprehensive TypeScript configuration

### Project Structure
```
IonAlumni/
├── API/                    # Backend API server
│   ├── src/               # Source code
│   │   ├── config/        # Configuration files
│   │   ├── controllers/   # Route controllers
│   │   ├── middleware/    # Express middleware
│   │   ├── routes/        # API routes
│   │   ├── services/      # Business logic services
│   │   ├── handlers/      # Socket.IO handlers
│   │   ├── types/         # TypeScript type definitions
│   │   └── utils/         # Utility functions
│   ├── prisma/            # Database schema and migrations
│   ├── uploads/           # File upload directory
│   └── dist/              # Compiled JavaScript (generated)
├── database/              # Database migration scripts
├── frontend/              # Next.js frontend (planned)
├── mobile/                # React Native app (planned)
└── README.md
```

## 📋 Prerequisites

Before setting up the project, ensure you have the following installed:

- **Node.js** (v18.0.0 or higher) - [Download](https://nodejs.org/)
- **npm** (v8.0.0 or higher) or **yarn** (v1.22.0 or higher)
- **MySQL** (v8.0 or higher) - [Download](https://dev.mysql.com/downloads/)
- **Redis** (v6.0 or higher) - [Download](https://redis.io/download) (Optional but recommended)
- **Git** - [Download](https://git-scm.com/downloads)

### System Requirements
- **OS**: Windows 10/11, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: At least 2GB free space

## 🛠️ Installation & Setup

### 1. Clone the Repository

```bash
git clone https://github.com/sushmitkumarpatil/IonAlumni.git
cd IonAlumni
```

### 2. Backend API Setup

#### Navigate to API Directory
```bash
cd API
```

#### Install Dependencies
```bash
# Using npm
npm install

# Or using yarn
yarn install
```

#### Environment Configuration
1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your configuration:
   ```env
   # Database Configuration
   DATABASE_URL="mysql://username:password@localhost:3306/ionalumni"

   # Server Configuration
   NODE_ENV=development
   PORT=5000
   FRONTEND_URL=http://localhost:3000

   # JWT Configuration (Generate secure keys)
   JWT_SECRET=your-super-secret-jwt-key-here-min-32-chars
   JWT_REFRESH_SECRET=your-super-secret-refresh-key-here-min-32-chars
   JWT_EXPIRES_IN=7d
   JWT_REFRESH_EXPIRES_IN=30d

   # Bcrypt Configuration
   BCRYPT_SALT_ROUNDS=12

   # Email Configuration (Optional - for notifications)
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-app-password
   FROM_EMAIL=<EMAIL>
   FROM_NAME=IonAlumni

   # Redis Configuration (Optional but recommended)
   REDIS_URL=redis://localhost:6379
   REDIS_PASSWORD=
   REDIS_DB=0

   # File Upload Configuration
   MAX_FILE_SIZE=10485760
   UPLOAD_PATH=./uploads/
   ```

### 3. Database Setup

#### Create MySQL Database
```sql
-- Connect to MySQL as root or admin user
CREATE DATABASE ionalumni CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create a dedicated user (recommended)
CREATE USER 'ionalumni_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON ionalumni.* TO 'ionalumni_user'@'localhost';
FLUSH PRIVILEGES;
```

#### Update Database URL
Update your `.env` file with the correct database credentials:
```env
DATABASE_URL="mysql://ionalumni_user:secure_password@localhost:3306/ionalumni"
```

### 4. Prisma Setup

#### Generate Prisma Client
```bash
npm run db:generate
```

#### Run Database Migrations
```bash
# Push the schema to your database (for development)
npm run db:push

# Or run migrations (recommended for production)
npm run db:migrate
```

#### Seed the Database
The seed script will create sample data including tenants, courses, and users:

```bash
npm run db:seed
```

**Default Seed Data Created:**
- **Tenants**:
  - Indian Institute of Technology (subdomain: `iit`)
  - Manipal Institute of Technology (subdomain: `mit`)
  - Vellore Institute of Technology (subdomain: `vit`)

- **Admin Users** (password: `AdminPass123!`):
  - `<EMAIL>` (IIT Admin)
  - `<EMAIL>` (MIT Admin)
  - `<EMAIL>` (VIT Admin)

- **Sample Alumni** (password: `AlumniPass123!`):
  - Multiple alumni users across different courses and batch years

- **Sample Students** (password: `StudentPass123!`):
  - Current students across various courses

### 5. Redis Setup (Optional but Recommended)

#### Install Redis
**Windows:**
```bash
# Using Chocolatey
choco install redis-64

# Or download from: https://github.com/microsoftarchive/redis/releases
```

**macOS:**
```bash
# Using Homebrew
brew install redis

# Start Redis
brew services start redis
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install redis-server

# Start Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### Verify Redis Installation
```bash
redis-cli ping
# Should return: PONG
```

### 6. Start the Development Server

#### Run in Development Mode
```bash
npm run dev
```

The server will start on `http://localhost:5000` with the following endpoints:
- **API Base**: `http://localhost:5000/api`
- **API Documentation**: `http://localhost:5000/api-docs`
- **Health Check**: `http://localhost:5000/health`

#### Build for Production
```bash
# Build the project
npm run build

# Start production server
npm start
```

## 📚 Available Scripts

### Development Scripts
```bash
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm start           # Start production server
npm run clean       # Clean build directory
```

### Database Scripts
```bash
npm run db:generate     # Generate Prisma client
npm run db:push        # Push schema to database (development)
npm run db:migrate     # Run database migrations
npm run db:migrate:prod # Deploy migrations (production)
npm run db:reset       # Reset database and run migrations
npm run db:seed        # Seed database with sample data
npm run db:studio      # Open Prisma Studio (database GUI)
```

### Code Quality Scripts
```bash
npm run lint           # Run ESLint with zero warnings tolerance
npm run lint:fix       # Fix auto-fixable ESLint issues
npm run lint:check     # Check for linting errors
npm run format         # Format code with Prettier
npm run format:check   # Check if code is properly formatted
npm run type-check     # Run TypeScript type checking
npm run code-quality   # Run all quality checks
npm run code-quality:fix # Fix all auto-fixable issues
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DATABASE_URL` | MySQL connection string | - | ✅ |
| `NODE_ENV` | Environment mode | `development` | ❌ |
| `PORT` | Server port | `5000` | ❌ |
| `FRONTEND_URL` | Frontend application URL | `http://localhost:3000` | ❌ |
| `JWT_SECRET` | JWT signing secret | - | ✅ |
| `JWT_REFRESH_SECRET` | JWT refresh token secret | - | ✅ |
| `JWT_EXPIRES_IN` | JWT expiration time | `7d` | ❌ |
| `JWT_REFRESH_EXPIRES_IN` | Refresh token expiration | `30d` | ❌ |
| `BCRYPT_SALT_ROUNDS` | Password hashing rounds | `12` | ❌ |
| `REDIS_URL` | Redis connection string | `redis://localhost:6379` | ❌ |
| `MAX_FILE_SIZE` | Maximum upload file size | `10485760` (10MB) | ❌ |
| `UPLOAD_PATH` | File upload directory | `./uploads/` | ❌ |

### Security Configuration

#### Generate Secure JWT Secrets
```bash
# Generate random 32-character strings for JWT secrets
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

#### Rate Limiting
The API includes built-in rate limiting:
- **Default**: 100 requests per 15 minutes per IP
- **Configurable** via environment variables

#### CORS Configuration
Configure allowed origins in your environment:
```env
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
```

## 🔌 API Documentation

### Swagger/OpenAPI Documentation
Once the server is running, access the interactive API documentation at:
- **Swagger UI**: `http://localhost:5000/api-docs`

### Key API Endpoints

#### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh JWT token
- `POST /api/auth/logout` - User logout
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password

#### User Management
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `POST /api/users/upload-avatar` - Upload profile picture
- `GET /api/users/search` - Search users

### Authentication
All protected endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## 🧪 Testing

### Manual Testing
1. **Health Check**: Visit `http://localhost:5000/health`
2. **API Documentation**: Visit `http://localhost:5000/api-docs`
3. **Database**: Use `npm run db:studio` to inspect data

### Test User Accounts
After running the seed script, you can use these test accounts:

**Admin Accounts** (password: `AdminPass123!`):
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`

**Alumni Accounts** (password: `AlumniPass123!`):
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`

**Student Accounts** (password: `StudentPass123!`):
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`

## 🚨 Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check if MySQL is running
sudo systemctl status mysql  # Linux
brew services list | grep mysql  # macOS

# Test database connection
mysql -u ionalumni_user -p -h localhost ionalumni
```

#### Port Already in Use
```bash
# Find process using port 5000
lsof -i :5000  # macOS/Linux
netstat -ano | findstr :5000  # Windows

# Kill the process
kill -9 <PID>  # macOS/Linux
taskkill /PID <PID> /F  # Windows
```

#### Prisma Issues
```bash
# Reset Prisma client
rm -rf node_modules/.prisma
npm run db:generate

# Reset database completely
npm run db:reset
npm run db:seed
```

#### Redis Connection Issues
```bash
# Check Redis status
redis-cli ping

# Start Redis service
sudo systemctl start redis-server  # Linux
brew services start redis  # macOS
```

### Environment Issues
- Ensure all required environment variables are set
- Check `.env` file syntax (no spaces around `=`)
- Verify database credentials and connection string
- Ensure JWT secrets are at least 32 characters long

### File Upload Issues
- Check `UPLOAD_PATH` directory exists and has write permissions
- Verify `MAX_FILE_SIZE` is appropriate for your needs
- Ensure disk space is available

## 📝 Development Guidelines

### Code Style
- **TypeScript**: Strict mode enabled with comprehensive type checking
- **ESLint**: Enforced code quality rules
- **Prettier**: Consistent code formatting
- **Naming**: Use camelCase for variables, PascalCase for classes

### Git Workflow
1. Create feature branches from `main`
2. Run code quality checks before committing:
   ```bash
   npm run code-quality
   ```
3. Write descriptive commit messages
4. Create pull requests for code review

### Database Changes
1. Create Prisma migrations for schema changes:
   ```bash
   npx prisma migrate dev --name your-migration-name
   ```
2. Update seed data if necessary
3. Test migrations on a copy of production data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and test thoroughly
4. Run code quality checks: `npm run code-quality`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

## 📄 License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details.

## 👥 Support

For support and questions:
- **Email**: <EMAIL>
- **Issues**: [GitHub Issues](https://github.com/sushmitkumarpatil/IonAlumni/issues)
- **Documentation**: [API Documentation](http://localhost:5000/api-docs)

## 🔄 Version History

- **v1.0.0** - Initial release with core features
  - Multi-tenant architecture
  - User authentication and authorization
  - Real-time communication
  - Job and event management
  - File upload capabilities

---

**Happy Coding! 🚀**
