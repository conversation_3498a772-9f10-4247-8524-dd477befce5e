import express from 'express';
import { body } from 'express-validator';
import multer from 'multer';
import { validateRequest } from '../middleware/validateRequest';
import { authenticateToken } from '../middleware/auth';
import * as ProfileController from '../controllers/ProfileController';

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

/**
 * @swagger
 * /api/profile:
 *   get:
 *     summary: Get current user's profile
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *       401:
 *         description: Not authenticated
 */
router.get('/', authenticateToken, ProfileController.getProfile);

/**
 * @swagger
 * /api/profile:
 *   put:
 *     summary: Update current user's profile
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               full_name:
 *                 type: string
 *               mobile_number:
 *                 type: string
 *               current_location:
 *                 type: string
 *               company:
 *                 type: string
 *               job_title:
 *                 type: string
 *               linkedin_url:
 *                 type: string
 *               batch_year:
 *                 type: integer
 *               course_id:
 *                 type: integer
 *               privacy_settings:
 *                 type: object
 *                 properties:
 *                   show_email:
 *                     type: boolean
 *                   show_mobile:
 *                     type: boolean
 *                   show_linkedin:
 *                     type: boolean
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Not authenticated
 */
router.put(
  '/',
  authenticateToken,
  [
    body('full_name').optional().isLength({ min: 2 }).trim(),
    body('mobile_number').optional().isMobilePhone('any'),
    body('current_location').optional().isLength({ min: 1 }).trim(),
    body('company').optional().isLength({ min: 1 }).trim(),
    body('job_title').optional().isLength({ min: 1 }).trim(),
    body('linkedin_url').optional().isURL(),
    body('batch_year')
      .optional()
      .isInt({ min: 1900, max: new Date().getFullYear() + 10 }),
    body('course_id').optional().isInt({ min: 1 }),
    body('privacy_settings.show_email').optional().isBoolean(),
    body('privacy_settings.show_mobile').optional().isBoolean(),
    body('privacy_settings.show_linkedin').optional().isBoolean(),
  ],
  validateRequest,
  ProfileController.updateProfile
);

/**
 * @swagger
 * /api/profile/picture:
 *   post:
 *     summary: Upload profile picture
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               picture:
 *                 type: string
 *                 format: binary
 *     responses:
 *       200:
 *         description: Profile picture uploaded successfully
 *       400:
 *         description: Invalid file or validation error
 *       401:
 *         description: Not authenticated
 */
router.post(
  '/picture',
  authenticateToken,
  upload.single('picture'),
  ProfileController.uploadProfilePicture
);

/**
 * @swagger
 * /api/profile/picture:
 *   delete:
 *     summary: Remove profile picture
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Profile picture removed successfully
 *       401:
 *         description: Not authenticated
 */
router.delete(
  '/picture',
  authenticateToken,
  ProfileController.removeProfilePicture
);

/**
 * @swagger
 * /api/profile/courses:
 *   get:
 *     summary: Get available courses for user's tenant
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Courses retrieved successfully
 *       401:
 *         description: Not authenticated
 */
router.get('/courses', authenticateToken, ProfileController.getCourses);

/**
 * @swagger
 * /api/profile/privacy:
 *   get:
 *     summary: Get current privacy settings
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Privacy settings retrieved successfully
 *       401:
 *         description: Not authenticated
 */
router.get('/privacy', authenticateToken, ProfileController.getPrivacySettings);

/**
 * @swagger
 * /api/profile/privacy:
 *   put:
 *     summary: Update privacy settings
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               show_email:
 *                 type: boolean
 *               show_mobile:
 *                 type: boolean
 *               show_linkedin:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Privacy settings updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Not authenticated
 */
router.put(
  '/privacy',
  authenticateToken,
  [
    body('show_email').optional().isBoolean(),
    body('show_mobile').optional().isBoolean(),
    body('show_linkedin').optional().isBoolean(),
  ],
  validateRequest,
  ProfileController.updatePrivacySettings
);

export default router;
