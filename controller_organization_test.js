const http = require('http');

// Test results tracker
const results = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, passed, details = '') {
  results.tests.push({ name, passed, details });
  if (passed) {
    results.passed++;
    console.log(`✅ ${name}`);
    if (details) console.log(`   ${details}`);
  } else {
    results.failed++;
    console.log(`❌ ${name}: ${details}`);
  }
}

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: responseData
        });
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(data);
    }
    req.end();
  });
}

async function testControllerOrganization() {
  console.log('🏗️ CONTROLLER ORGANIZATION VALIDATION TEST');
  console.log('='.repeat(60));
  console.log('Testing new organized controller structure and endpoints...\n');

  let cookies = null;

  try {
    // Test 1: Account Controller (Registration & Password Management)
    console.log('📝 SECTION 1: ACCOUNT CONTROLLER');
    console.log('-'.repeat(50));
    
    // Test account registration endpoint
    const registerData = JSON.stringify({
      email: '<EMAIL>',
      password: 'TestPass123!',
      full_name: 'Test Organization User',
      role: 'STUDENT',
      tenant_id: 1,
      usn: 'ORG2024001',
      course_name: 'Computer Science'
    });

    const registerResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api/account/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(registerData)
      }
    }, registerData);

    logTest('Account registration endpoint', 
      registerResponse.statusCode === 201 || registerResponse.statusCode === 409,
      `Status: ${registerResponse.statusCode} (201 = success, 409 = user exists)`
    );

    // Test forgot password endpoint
    const forgotPasswordData = JSON.stringify({
      email: '<EMAIL>',
      tenant_id: 8
    });

    const forgotPasswordResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api/account/forgot-password',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(forgotPasswordData)
      }
    }, forgotPasswordData);

    logTest('Forgot password endpoint', 
      forgotPasswordResponse.statusCode === 200,
      `Status: ${forgotPasswordResponse.statusCode}`
    );

    // Test 2: Authentication Controller (Login & Token Management)
    console.log('\n🔐 SECTION 2: AUTHENTICATION CONTROLLER');
    console.log('-'.repeat(50));
    
    const loginData = JSON.stringify({
      email: '<EMAIL>',
      password: 'AdminPass123!',
      tenant_id: 8
    });

    const loginResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginData)
      }
    }, loginData);

    cookies = loginResponse.headers['set-cookie'];
    
    logTest('Authentication login endpoint', 
      loginResponse.statusCode === 200 && cookies,
      `Status: ${loginResponse.statusCode}, Cookies set: ${!!cookies}`
    );

    // Test current user endpoint
    if (cookies) {
      const currentUserResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/auth/me',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('Get current user endpoint', 
        currentUserResponse.statusCode === 200,
        `Status: ${currentUserResponse.statusCode}`
      );
    }

    // Test 3: Profile Controller (User Profile Management)
    console.log('\n👤 SECTION 3: PROFILE CONTROLLER');
    console.log('-'.repeat(50));
    
    if (cookies) {
      const profileResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/profile',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('Get user profile endpoint', 
        profileResponse.statusCode === 200,
        `Status: ${profileResponse.statusCode}`
      );

      // Test courses endpoint
      const coursesResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/profile/courses',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('Get available courses endpoint', 
        coursesResponse.statusCode === 200,
        `Status: ${coursesResponse.statusCode}`
      );

      // Test privacy settings endpoint
      const privacyResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/profile/privacy',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('Get privacy settings endpoint', 
        privacyResponse.statusCode === 200,
        `Status: ${privacyResponse.statusCode}`
      );
    }

    // Test 4: Directory Controller (User Directory & Search)
    console.log('\n📖 SECTION 4: DIRECTORY CONTROLLER');
    console.log('-'.repeat(50));
    
    if (cookies) {
      const directoryResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/directory?page=1&limit=5',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('User directory endpoint', 
        directoryResponse.statusCode === 200,
        `Status: ${directoryResponse.statusCode}`
      );

      // Test directory stats
      const statsResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/directory/stats',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('Directory statistics endpoint', 
        statsResponse.statusCode === 200,
        `Status: ${statsResponse.statusCode}`
      );

      // Test search functionality
      const searchResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/directory/search?q=admin',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('Directory search endpoint', 
        searchResponse.statusCode === 200,
        `Status: ${searchResponse.statusCode}`
      );
    }

    // Test 5: Connection Controller (User Networking)
    console.log('\n🤝 SECTION 5: CONNECTION CONTROLLER');
    console.log('-'.repeat(50));
    
    if (cookies) {
      const connectionsResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/connections',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('User connections endpoint', 
        connectionsResponse.statusCode === 200,
        `Status: ${connectionsResponse.statusCode}`
      );

      // Test connection requests
      const requestsResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/connections/requests?type=received',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('Connection requests endpoint', 
        requestsResponse.statusCode === 200,
        `Status: ${requestsResponse.statusCode}`
      );
    }

    // Test 6: Admin User Controller (User Management)
    console.log('\n👑 SECTION 6: ADMIN USER CONTROLLER');
    console.log('-'.repeat(50));
    
    if (cookies) {
      const adminUsersResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/admin/users?page=1&limit=5',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('Admin users management endpoint', 
        adminUsersResponse.statusCode === 200,
        `Status: ${adminUsersResponse.statusCode}`
      );

      // Test pending users
      const pendingUsersResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/admin/users/pending',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('Admin pending users endpoint', 
        pendingUsersResponse.statusCode === 200,
        `Status: ${pendingUsersResponse.statusCode}`
      );
    }

    // Test 7: Admin Dashboard Controller (Analytics & Reports)
    console.log('\n📊 SECTION 7: ADMIN DASHBOARD CONTROLLER');
    console.log('-'.repeat(50));
    
    if (cookies) {
      const dashboardResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/admin/dashboard',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('Admin dashboard endpoint', 
        dashboardResponse.statusCode === 200,
        `Status: ${dashboardResponse.statusCode}`
      );

      // Test user activity report
      const userActivityResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/admin/dashboard/user-activity',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('User activity report endpoint', 
        userActivityResponse.statusCode === 200,
        `Status: ${userActivityResponse.statusCode}`
      );

      // Test content activity report
      const contentActivityResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/admin/dashboard/content-activity',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('Content activity report endpoint', 
        contentActivityResponse.statusCode === 200,
        `Status: ${contentActivityResponse.statusCode}`
      );
    }

    // Test 8: Legacy Route Compatibility
    console.log('\n🔄 SECTION 8: LEGACY ROUTE COMPATIBILITY');
    console.log('-'.repeat(50));
    
    // Test that old routes still work for backward compatibility
    const legacyAuthResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginData)
      }
    }, loginData);

    logTest('Legacy auth route compatibility', 
      legacyAuthResponse.statusCode === 200,
      `Status: ${legacyAuthResponse.statusCode}`
    );

  } catch (error) {
    console.error('❌ Test execution error:', error.message);
    logTest('Test execution', false, error.message);
  }

  // Print comprehensive summary
  console.log('\n' + '='.repeat(60));
  console.log('🏗️ CONTROLLER ORGANIZATION TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 ALL CONTROLLER ORGANIZATION TESTS PASSED!');
    console.log('✨ New organized controller structure is working perfectly.');
    console.log('\n🔥 CONTROLLER ORGANIZATION ACHIEVEMENTS:');
    console.log('   ✅ AccountController - Registration & password management');
    console.log('   ✅ AuthenticationController - Login & token management');
    console.log('   ✅ ProfileController - User profile management');
    console.log('   ✅ DirectoryController - User directory & search');
    console.log('   ✅ ConnectionController - User networking features');
    console.log('   ✅ AdminUserController - User administration');
    console.log('   ✅ AdminDashboardController - Analytics & reports');
    console.log('   ✅ Legacy route compatibility maintained');
  } else {
    console.log('\n⚠️  Some controller organization tests failed. Review details above.');
  }
  
  console.log('\n📋 New Organized Endpoint Structure:');
  console.log('   • /api/account/* - Account management (registration, password reset)');
  console.log('   • /api/auth/* - Authentication (login, logout, token management)');
  console.log('   • /api/profile/* - User profiles (view, update, privacy settings)');
  console.log('   • /api/directory/* - User directory (search, browse, statistics)');
  console.log('   • /api/connections/* - User networking (connections, requests)');
  console.log('   • /api/admin/users/* - Admin user management');
  console.log('   • /api/admin/dashboard/* - Admin analytics & reports');
}

testControllerOrganization();
