import express from 'express';
import { param, query } from 'express-validator';
import { validateRequest } from '../middleware/validateRequest';
import { authenticateToken } from '../middleware/auth';
import * as DirectoryController from '../controllers/DirectoryController';

const router = express.Router();

/**
 * @swagger
 * /api/directory:
 *   get:
 *     summary: Get user directory with search and filtering
 *     tags: [Directory]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for name, email, company, or job title
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [STUDENT, ALUMNUS, ALL]
 *         description: Filter by user role
 *       - in: query
 *         name: course
 *         schema:
 *           type: string
 *         description: Filter by course name
 *       - in: query
 *         name: batch_year
 *         schema:
 *           type: integer
 *         description: Filter by batch year
 *       - in: query
 *         name: company
 *         schema:
 *           type: string
 *         description: Filter by company name
 *       - in: query
 *         name: location
 *         schema:
 *           type: string
 *         description: Filter by current location
 *     responses:
 *       200:
 *         description: User directory retrieved successfully
 *       401:
 *         description: Not authenticated
 */
router.get(
  '/',
  authenticateToken,
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('search').optional().isLength({ min: 1, max: 100 }).trim(),
    query('role').optional().isIn(['STUDENT', 'ALUMNUS', 'ALL']),
    query('course').optional().isLength({ min: 1, max: 100 }).trim(),
    query('batch_year').optional().isInt({ min: 1900, max: new Date().getFullYear() + 10 }),
    query('company').optional().isLength({ min: 1, max: 100 }).trim(),
    query('location').optional().isLength({ min: 1, max: 100 }).trim(),
  ],
  validateRequest,
  DirectoryController.getUserDirectory
);

/**
 * @swagger
 * /api/directory/{id}:
 *   get:
 *     summary: Get user by ID
 *     tags: [Directory]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: User retrieved successfully
 *       401:
 *         description: Not authenticated
 *       404:
 *         description: User not found
 */
router.get(
  '/:id',
  authenticateToken,
  [param('id').isInt({ min: 1 })],
  validateRequest,
  DirectoryController.getUserById
);

/**
 * @swagger
 * /api/directory/stats:
 *   get:
 *     summary: Get directory statistics
 *     tags: [Directory]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Directory statistics retrieved successfully
 *       401:
 *         description: Not authenticated
 */
router.get('/stats', authenticateToken, DirectoryController.getDirectoryStats);

/**
 * @swagger
 * /api/directory/search:
 *   get:
 *     summary: Advanced user search
 *     tags: [Directory]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 2
 *         description: Search query (minimum 2 characters)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Search results retrieved successfully
 *       400:
 *         description: Search query too short
 *       401:
 *         description: Not authenticated
 */
router.get(
  '/search',
  authenticateToken,
  [
    query('q').isLength({ min: 2, max: 100 }).trim(),
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
  ],
  validateRequest,
  DirectoryController.searchUsers
);

export default router;
