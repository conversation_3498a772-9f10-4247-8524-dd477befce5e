import express from 'express';
import { body, param, query } from 'express-validator';
import { validateRequest } from '../middleware/validateRequest';
import { authenticateToken, requireRole } from '../middleware/auth';
import * as AdminUserController from '../controllers/AdminUserController';

const router = express.Router();

/**
 * @swagger
 * /api/admin/users:
 *   get:
 *     summary: Get all users with filtering and pagination
 *     tags: [Admin - Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for name, email, or USN
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [STUDENT, ALUMNUS, TENANT_ADMIN]
 *         description: Filter by user role
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, APPROVED, REJECTED, DEACTIVATED]
 *         description: Filter by account status
 *       - in: query
 *         name: tenant_id
 *         schema:
 *           type: integer
 *         description: Filter by tenant ID (Super Admin only)
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/',
  authenticateToken,
  requireRole(['TENANT_ADMIN', 'SUPER_ADMIN']),
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('search').optional().isLength({ min: 1, max: 100 }).trim(),
    query('role').optional().isIn(['STUDENT', 'ALUMNUS', 'TENANT_ADMIN']),
    query('status')
      .optional()
      .isIn(['PENDING', 'APPROVED', 'REJECTED', 'DEACTIVATED']),
    query('tenant_id').optional().isInt({ min: 1 }),
  ],
  validateRequest,
  AdminUserController.getUsers
);

/**
 * @swagger
 * /api/admin/users/pending:
 *   get:
 *     summary: Get users pending approval
 *     tags: [Admin - Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Pending users retrieved successfully
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/pending',
  authenticateToken,
  requireRole(['TENANT_ADMIN', 'SUPER_ADMIN']),
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
  ],
  validateRequest,
  AdminUserController.getPendingUsers
);

/**
 * @swagger
 * /api/admin/users/{id}:
 *   get:
 *     summary: Get single user by ID
 *     tags: [Admin - Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: User retrieved successfully
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: User not found
 */
router.get(
  '/:id',
  authenticateToken,
  requireRole(['TENANT_ADMIN', 'SUPER_ADMIN']),
  [param('id').isInt({ min: 1 })],
  validateRequest,
  AdminUserController.getUserById
);

/**
 * @swagger
 * /api/admin/users/{id}/status:
 *   put:
 *     summary: Update user status
 *     tags: [Admin - Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [APPROVED, REJECTED, DEACTIVATED, PENDING]
 *               reason:
 *                 type: string
 *                 maxLength: 500
 *     responses:
 *       200:
 *         description: User status updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: User not found
 */
router.put(
  '/:id/status',
  authenticateToken,
  requireRole(['TENANT_ADMIN', 'SUPER_ADMIN']),
  [
    param('id').isInt({ min: 1 }),
    body('status').isIn(['APPROVED', 'REJECTED', 'DEACTIVATED', 'PENDING']),
    body('reason').optional().isLength({ max: 500 }).trim(),
  ],
  validateRequest,
  AdminUserController.updateUserStatus
);

/**
 * @swagger
 * /api/admin/users/{id}/role:
 *   put:
 *     summary: Update user role (Super Admin only)
 *     tags: [Admin - Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - role
 *             properties:
 *               role:
 *                 type: string
 *                 enum: [STUDENT, ALUMNUS, TENANT_ADMIN]
 *     responses:
 *       200:
 *         description: User role updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Insufficient permissions (Super Admin only)
 *       404:
 *         description: User not found
 */
router.put(
  '/:id/role',
  authenticateToken,
  requireRole(['SUPER_ADMIN']),
  [
    param('id').isInt({ min: 1 }),
    body('role').isIn(['STUDENT', 'ALUMNUS', 'TENANT_ADMIN']),
  ],
  validateRequest,
  AdminUserController.updateUserRole
);

/**
 * @swagger
 * /api/admin/users/bulk-status:
 *   put:
 *     summary: Bulk update user status
 *     tags: [Admin - Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_ids
 *               - status
 *             properties:
 *               user_ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 minItems: 1
 *               status:
 *                 type: string
 *                 enum: [APPROVED, REJECTED, DEACTIVATED]
 *               reason:
 *                 type: string
 *                 maxLength: 500
 *     responses:
 *       200:
 *         description: Users updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: No eligible users found
 */
router.put(
  '/bulk-status',
  authenticateToken,
  requireRole(['TENANT_ADMIN', 'SUPER_ADMIN']),
  [
    body('user_ids').isArray({ min: 1 }),
    body('user_ids.*').isInt({ min: 1 }),
    body('status').isIn(['APPROVED', 'REJECTED', 'DEACTIVATED']),
    body('reason').optional().isLength({ max: 500 }).trim(),
  ],
  validateRequest,
  AdminUserController.bulkUpdateUserStatus
);

export default router;
