const http = require('http');

// Test results tracker
const results = {
  passed: 0,
  failed: 0,
  tests: [],
  sections: {}
};

function logTest(section, name, passed, details = '') {
  if (!results.sections[section]) {
    results.sections[section] = { passed: 0, failed: 0, tests: [] };
  }
  
  const test = { name, passed, details };
  results.tests.push({ section, ...test });
  results.sections[section].tests.push(test);
  
  if (passed) {
    results.passed++;
    results.sections[section].passed++;
    console.log(`✅ ${name}`);
    if (details) console.log(`   ${details}`);
  } else {
    results.failed++;
    results.sections[section].failed++;
    console.log(`❌ ${name}: ${details}`);
  }
}

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: responseData
        });
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(data);
    }
    req.end();
  });
}

async function comprehensiveFunctionalityTest() {
  console.log('🔍 COMPREHENSIVE FUNCTIONALITY TEST');
  console.log('='.repeat(70));
  console.log('Testing ALL system functionality with detailed validation...\n');

  let cookies = null;
  let testUserId = null;

  try {
    // Test 1: System Health & Infrastructure
    console.log('🏥 SECTION 1: SYSTEM HEALTH & INFRASTRUCTURE');
    console.log('-'.repeat(60));
    
    // Health check
    const healthResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/health',
      method: 'GET'
    });

    const healthData = JSON.parse(healthResponse.data);
    logTest('infrastructure', 'Health check endpoint', 
      healthResponse.statusCode === 200 && healthData.status === 'OK',
      `Status: ${healthResponse.statusCode}, Server status: ${healthData.status}`
    );

    // Ready check
    const readyResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/ready',
      method: 'GET'
    });

    logTest('infrastructure', 'Ready check endpoint', 
      readyResponse.statusCode === 200,
      `Status: ${readyResponse.statusCode}`
    );

    // API docs
    const docsResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api-docs.json',
      method: 'GET'
    });

    logTest('infrastructure', 'API documentation endpoint', 
      docsResponse.statusCode === 200,
      `Status: ${docsResponse.statusCode}`
    );

    // Test 2: Authentication System
    console.log('\n🔐 SECTION 2: AUTHENTICATION SYSTEM');
    console.log('-'.repeat(60));
    
    // Valid login
    const loginData = JSON.stringify({
      email: '<EMAIL>',
      password: 'AdminPass123!',
      tenant_id: 8
    });

    const loginResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginData)
      }
    }, loginData);

    const loginResult = JSON.parse(loginResponse.data);
    cookies = loginResponse.headers['set-cookie'];
    
    logTest('authentication', 'Valid login', 
      loginResponse.statusCode === 200 && loginResult.success && cookies,
      `Status: ${loginResponse.statusCode}, Cookies set: ${!!cookies}`
    );

    logTest('authentication', 'Login response structure', 
      loginResult.user && loginResult.timestamp && !loginResult.user.password_hash,
      'Has user object, timestamp, no sensitive data'
    );

    // Invalid login
    const invalidLoginData = JSON.stringify({
      email: '<EMAIL>',
      password: 'WrongPassword',
      tenant_id: 8
    });

    const invalidLoginResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(invalidLoginData)
      }
    }, invalidLoginData);

    logTest('authentication', 'Invalid login rejection', 
      invalidLoginResponse.statusCode === 401,
      `Status: ${invalidLoginResponse.statusCode}`
    );

    // Missing tenant login
    const noTenantLoginData = JSON.stringify({
      email: '<EMAIL>',
      password: 'AdminPass123!'
    });

    const noTenantLoginResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(noTenantLoginData)
      }
    }, noTenantLoginData);

    logTest('authentication', 'Missing tenant validation', 
      noTenantLoginResponse.statusCode === 400,
      `Status: ${noTenantLoginResponse.statusCode}`
    );

    // Test 3: User Profile Management
    console.log('\n👤 SECTION 3: USER PROFILE MANAGEMENT');
    console.log('-'.repeat(60));
    
    if (cookies) {
      // Get profile
      const profileResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/users/profile',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      const profileResult = JSON.parse(profileResponse.data);
      testUserId = profileResult.data?.id;
      
      logTest('profile', 'Get user profile', 
        profileResponse.statusCode === 200 && profileResult.success,
        `Status: ${profileResponse.statusCode}, User ID: ${testUserId}`
      );

      logTest('profile', 'Profile data completeness', 
        profileResult.data?.email && profileResult.data?.full_name && profileResult.data?.role,
        'Has email, full_name, and role'
      );

      logTest('profile', 'Profile privacy filtering', 
        !profileResult.data?.password_hash && !profileResult.data?.reset_token,
        'No sensitive fields exposed'
      );

      // Update profile
      const updateData = JSON.stringify({
        full_name: 'Updated Admin User',
        mobile_number: '+1234567890'
      });

      const updateResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/users/profile',
        method: 'PUT',
        headers: {
          'Cookie': cookies.join('; '),
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(updateData)
        }
      }, updateData);

      logTest('profile', 'Update user profile', 
        updateResponse.statusCode === 200,
        `Status: ${updateResponse.statusCode}`
      );
    }

    // Test 4: User Directory & Search
    console.log('\n📖 SECTION 4: USER DIRECTORY & SEARCH');
    console.log('-'.repeat(60));
    
    if (cookies) {
      // Get directory
      const directoryResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/users/directory?page=1&limit=5',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      const directoryResult = JSON.parse(directoryResponse.data);
      
      logTest('directory', 'User directory listing', 
        directoryResponse.statusCode === 200 && directoryResult.success,
        `Status: ${directoryResponse.statusCode}, Users: ${directoryResult.data?.length || 0}`
      );

      logTest('directory', 'Directory pagination', 
        directoryResult.pagination && directoryResult.pagination.page === 1,
        `Page: ${directoryResult.pagination?.page}, Total: ${directoryResult.pagination?.total}`
      );

      // Search users
      const searchResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/users/directory?search=admin&page=1&limit=5',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      const searchResult = JSON.parse(searchResponse.data);
      
      logTest('directory', 'User search functionality', 
        searchResponse.statusCode === 200 && searchResult.success,
        `Status: ${searchResponse.statusCode}, Results: ${searchResult.data?.length || 0}`
      );

      // Get specific user
      if (testUserId) {
        const userResponse = await makeRequest({
          hostname: 'localhost',
          port: 5000,
          path: `/api/users/${testUserId}`,
          method: 'GET',
          headers: {
            'Cookie': cookies.join('; ')
          }
        });

        logTest('directory', 'Get specific user', 
          userResponse.statusCode === 200,
          `Status: ${userResponse.statusCode}`
        );
      }
    }

    // Test 5: Admin Dashboard & Analytics
    console.log('\n📊 SECTION 5: ADMIN DASHBOARD & ANALYTICS');
    console.log('-'.repeat(60));
    
    if (cookies) {
      // Dashboard stats
      const dashboardResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/admin/dashboard',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      const dashboardResult = JSON.parse(dashboardResponse.data);
      
      logTest('admin', 'Admin dashboard access', 
        dashboardResponse.statusCode === 200 && dashboardResult.success,
        `Status: ${dashboardResponse.statusCode}`
      );

      logTest('admin', 'Dashboard statistics completeness', 
        dashboardResult.data?.users && dashboardResult.data?.content,
        'Has user and content statistics'
      );

      // User analytics
      const analyticsResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/admin/analytics/users',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('admin', 'User analytics endpoint', 
        analyticsResponse.statusCode === 200,
        `Status: ${analyticsResponse.statusCode}`
      );
    }

    // Test 6: Admin User Management
    console.log('\n👑 SECTION 6: ADMIN USER MANAGEMENT');
    console.log('-'.repeat(60));
    
    if (cookies) {
      // Get all users
      const usersResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/admin/users?page=1&limit=5',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      const usersResult = JSON.parse(usersResponse.data);
      
      logTest('admin', 'Admin user listing', 
        usersResponse.statusCode === 200 && usersResult.success,
        `Status: ${usersResponse.statusCode}, Users: ${usersResult.data?.length || 0}`
      );

      logTest('admin', 'Admin user pagination', 
        usersResult.pagination && typeof usersResult.pagination.total === 'number',
        `Total users: ${usersResult.pagination?.total}`
      );

      // Search users
      const adminSearchResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/admin/users?search=admin&page=1&limit=5',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('admin', 'Admin user search', 
        adminSearchResponse.statusCode === 200,
        `Status: ${adminSearchResponse.statusCode}`
      );

      // Filter by status
      const statusFilterResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/admin/users?status=APPROVED&page=1&limit=5',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('admin', 'Admin user status filtering', 
        statusFilterResponse.statusCode === 200,
        `Status: ${statusFilterResponse.statusCode}`
      );
    }

    // Test 7: Content Management
    console.log('\n📝 SECTION 7: CONTENT MANAGEMENT');
    console.log('-'.repeat(60));
    
    if (cookies) {
      // Get posts
      const postsResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/users/posts?page=1&limit=5',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('content', 'Posts listing', 
        postsResponse.statusCode === 200,
        `Status: ${postsResponse.statusCode}`
      );

      // Get jobs
      const jobsResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/users/jobs?page=1&limit=5',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('content', 'Jobs listing', 
        jobsResponse.statusCode === 200,
        `Status: ${jobsResponse.statusCode}`
      );

      // Get events
      const eventsResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/users/events?page=1&limit=5',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      logTest('content', 'Events listing', 
        eventsResponse.statusCode === 200,
        `Status: ${eventsResponse.statusCode}`
      );
    }

    // Test 8: Error Handling & Security
    console.log('\n🛡️ SECTION 8: ERROR HANDLING & SECURITY');
    console.log('-'.repeat(60));
    
    // Unauthorized access
    const unauthorizedResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api/admin/users',
      method: 'GET'
    });

    const unauthorizedResult = JSON.parse(unauthorizedResponse.data);
    
    logTest('security', 'Unauthorized access protection', 
      unauthorizedResponse.statusCode === 401 && unauthorizedResult.error,
      `Status: ${unauthorizedResponse.statusCode}`
    );

    logTest('security', 'Error response structure', 
      unauthorizedResult.timestamp && unauthorizedResult.path && unauthorizedResult.method,
      'Has timestamp, path, and method'
    );

    // Invalid endpoint
    const notFoundResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api/nonexistent',
      method: 'GET'
    });

    logTest('security', '404 handling', 
      notFoundResponse.statusCode === 404,
      `Status: ${notFoundResponse.statusCode}`
    );

    // Invalid JSON
    const invalidJsonResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength('invalid json')
      }
    }, 'invalid json');

    logTest('security', 'Invalid JSON handling', 
      invalidJsonResponse.statusCode === 400,
      `Status: ${invalidJsonResponse.statusCode}`
    );

    // Test 9: Rate Limiting & Performance
    console.log('\n⚡ SECTION 9: RATE LIMITING & PERFORMANCE');
    console.log('-'.repeat(60));
    
    // Multiple rapid requests
    const rapidRequests = [];
    for (let i = 0; i < 5; i++) {
      rapidRequests.push(makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/health',
        method: 'GET'
      }));
    }

    const rapidResults = await Promise.all(rapidRequests);
    const allSuccessful = rapidResults.every(r => r.statusCode === 200);
    
    logTest('performance', 'Concurrent request handling', 
      allSuccessful,
      `All ${rapidResults.length} requests successful`
    );

    // Response time test
    const startTime = Date.now();
    await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/health',
      method: 'GET'
    });
    const responseTime = Date.now() - startTime;
    
    logTest('performance', 'Response time performance', 
      responseTime < 1000,
      `Response time: ${responseTime}ms`
    );

  } catch (error) {
    console.error('❌ Test execution error:', error.message);
    logTest('system', 'Test execution', false, error.message);
  }

  // Print comprehensive summary
  console.log('\n' + '='.repeat(70));
  console.log('🔍 COMPREHENSIVE FUNCTIONALITY TEST SUMMARY');
  console.log('='.repeat(70));
  
  // Section-wise summary
  Object.keys(results.sections).forEach(section => {
    const sectionData = results.sections[section];
    const successRate = ((sectionData.passed / (sectionData.passed + sectionData.failed)) * 100).toFixed(1);
    console.log(`📋 ${section.toUpperCase()}: ${sectionData.passed}/${sectionData.passed + sectionData.failed} (${successRate}%)`);
  });
  
  console.log(`\n✅ Total Passed: ${results.passed}`);
  console.log(`❌ Total Failed: ${results.failed}`);
  console.log(`📈 Overall Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 ALL FUNCTIONALITY TESTS PASSED!');
    console.log('✨ System is fully operational and all features working correctly.');
    console.log('\n🔥 COMPREHENSIVE VALIDATION COMPLETE:');
    console.log('   ✅ System infrastructure and health checks');
    console.log('   ✅ Authentication and security measures');
    console.log('   ✅ User profile management');
    console.log('   ✅ Directory and search functionality');
    console.log('   ✅ Admin dashboard and analytics');
    console.log('   ✅ User management and administration');
    console.log('   ✅ Content management systems');
    console.log('   ✅ Error handling and security');
    console.log('   ✅ Performance and rate limiting');
  } else {
    console.log('\n⚠️  Some functionality tests failed. Review details above.');
    
    // Show failed tests
    const failedTests = results.tests.filter(t => !t.passed);
    if (failedTests.length > 0) {
      console.log('\n❌ Failed Tests:');
      failedTests.forEach(test => {
        console.log(`   • ${test.section}: ${test.name} - ${test.details}`);
      });
    }
  }
}

comprehensiveFunctionalityTest();
