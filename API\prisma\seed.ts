/// <reference types="node" />
import { PrismaClient, UserRole, UserStatus } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create multiple tenants with realistic examples
  const tenants = [
    {
      name: 'Indian Institute of Technology Bombay',
      subdomain: 'iitb',
      logo_url: 'https://www.iitb.ac.in/sites/default/files/iitb-logo.png',
      is_active: true,
    },
    {
      name: 'National Institute of Technology Karnataka',
      subdomain: 'nitk',
      logo_url: 'https://nitk.ac.in/images/nitk-logo.png',
      is_active: true,
    },
    {
      name: 'Indian Institute of Management Bangalore',
      subdomain: 'iimb',
      logo_url: 'https://www.iimb.ac.in/sites/default/files/iimb-logo.png',
      is_active: true,
    },
    {
      name: 'Delhi University',
      subdomain: 'du',
      logo_url: 'https://www.du.ac.in/images/du-logo.png',
      is_active: true,
    },
    {
      name: 'Manipal Institute of Technology',
      subdomain: 'mit',
      logo_url:
        'https://manipal.edu/content/dam/manipal/mu/mit/images/mit-logo.png',
      is_active: true,
    },
    {
      name: 'Vellore Institute of Technology',
      subdomain: 'vit',
      logo_url: 'https://vit.ac.in/images/vit-logo.png',
      is_active: true,
    },
  ];

  console.log('🏫 Creating tenants...');
  const createdTenants: any[] = [];

  for (const tenantData of tenants) {
    const tenant = await prisma.tenant.upsert({
      where: { subdomain: tenantData.subdomain },
      update: tenantData,
      create: tenantData,
    });
    createdTenants.push(tenant);
    console.log(`✅ Created/Updated: ${tenant.name} (${tenant.subdomain})`);
  }

  // Create courses for each tenant
  console.log('📚 Creating courses...');

  const coursesByTenant: Record<string, string[]> = {
    iitb: [
      'Computer Science and Engineering',
      'Electrical Engineering',
      'Mechanical Engineering',
      'Civil Engineering',
      'Chemical Engineering',
      'Aerospace Engineering',
      'Metallurgical Engineering and Materials Science',
    ],
    nitk: [
      'Computer Science and Engineering',
      'Electronics and Communication Engineering',
      'Mechanical Engineering',
      'Civil Engineering',
      'Information Technology',
      'Electrical Engineering',
      'Chemical Engineering',
    ],
    iimb: [
      'Master of Business Administration',
      'Executive MBA',
      'Post Graduate Programme in Management',
      'Fellow Programme in Management',
      'Certificate Programme in General Management',
    ],
    du: [
      'Bachelor of Arts',
      'Bachelor of Science',
      'Bachelor of Commerce',
      'Master of Arts',
      'Master of Science',
      'Master of Commerce',
      'Bachelor of Technology',
    ],
    mit: [
      'Computer Science and Engineering',
      'Information Technology',
      'Electronics and Communication Engineering',
      'Mechanical Engineering',
      'Civil Engineering',
      'Biotechnology',
    ],
    vit: [
      'Computer Science and Engineering',
      'Electronics and Communication Engineering',
      'Mechanical Engineering',
      'Civil Engineering',
      'Information Technology',
      'Electrical and Electronics Engineering',
      'Chemical Engineering',
    ],
  };

  for (const tenant of createdTenants) {
    const courses = coursesByTenant[tenant.subdomain] || [];

    for (const courseName of courses) {
      // Check if course already exists
      const existingCourse = await prisma.course.findFirst({
        where: {
          tenant_id: tenant.id,
          course_name: courseName,
        },
      });

      if (!existingCourse) {
        await prisma.course.create({
          data: {
            tenant_id: tenant.id,
            course_name: courseName,
          },
        });
      }
    }

    console.log(`📖 Added ${courses.length} courses for ${tenant.name}`);
  }

  // Create admin users for each tenant
  console.log('👤 Creating admin users...');
  const adminPassword = await bcrypt.hash('AdminPass123!', 12);

  const adminEmails: Record<string, string> = {
    iitb: '<EMAIL>',
    nitk: '<EMAIL>',
    iimb: '<EMAIL>',
    du: '<EMAIL>',
    mit: '<EMAIL>',
    vit: '<EMAIL>',
  };

  for (const tenant of createdTenants) {
    const adminEmail = adminEmails[tenant.subdomain];
    if (adminEmail) {
      await prisma.user.upsert({
        where: {
          idx_tenant_email: {
            tenant_id: tenant.id,
            email: adminEmail,
          },
        },
        update: {},
        create: {
          tenant_id: tenant.id,
          email: adminEmail,
          password_hash: adminPassword,
          full_name: `${tenant.name} Administrator`,
          usn: `ADMIN${tenant.id.toString().padStart(3, '0')}`,
          role: UserRole.TENANT_ADMIN,
          account_status: UserStatus.APPROVED,
        },
      });
      console.log(`👨‍💼 Created admin for ${tenant.name}: ${adminEmail}`);
    }
  }

  // Create sample alumni users for each tenant
  console.log('🎓 Creating sample alumni users...');

  const sampleAlumniByTenant: Record<string, any[]> = {
    iitb: [
      {
        email: '<EMAIL>',
        full_name: 'Rajesh Kumar',
        usn: 'CS2018001',
        batch_year: 2018,
        course_name: 'Computer Science and Engineering',
        company: 'Google',
        job_title: 'Software Engineer',
        location: 'Bangalore, India',
      },
      {
        email: '<EMAIL>',
        full_name: 'Priya Sharma',
        usn: 'EE2017002',
        batch_year: 2017,
        course_name: 'Electrical Engineering',
        company: 'Microsoft',
        job_title: 'Senior Software Engineer',
        location: 'Hyderabad, India',
      },
    ],
    nitk: [
      {
        email: '<EMAIL>',
        full_name: 'Amit Patel',
        usn: 'CS2019001',
        batch_year: 2019,
        course_name: 'Computer Science and Engineering',
        company: 'Amazon',
        job_title: 'Software Development Engineer',
        location: 'Bangalore, India',
      },
    ],
    iimb: [
      {
        email: '<EMAIL>',
        full_name: 'Neha Gupta',
        usn: '**********',
        batch_year: 2020,
        course_name: 'Master of Business Administration',
        company: 'McKinsey & Company',
        job_title: 'Business Analyst',
        location: 'Mumbai, India',
      },
    ],
  };

  for (const tenant of createdTenants) {
    const alumniUsers = sampleAlumniByTenant[tenant.subdomain] || [];

    for (const userData of alumniUsers) {
      const password = await bcrypt.hash('AlumniPass123!', 12);

      // Find the course for this user
      const course = await prisma.course.findFirst({
        where: {
          tenant_id: tenant.id,
          course_name: userData.course_name,
        },
      });

      const user = await prisma.user.upsert({
        where: {
          idx_tenant_email: {
            tenant_id: tenant.id,
            email: userData.email,
          },
        },
        update: {},
        create: {
          tenant_id: tenant.id,
          email: userData.email,
          password_hash: password,
          full_name: userData.full_name,
          usn: userData.usn,
          role: UserRole.ALUMNUS,
          account_status: UserStatus.APPROVED,
        },
      });

      // Create user profile
      await prisma.userProfile.upsert({
        where: {
          user_id: user.id,
        },
        update: {},
        create: {
          user_id: user.id,
          tenant_id: tenant.id,
          course_id: course?.id || null,
          batch_year: userData.batch_year,
          current_location: userData.location,
          company: userData.company,
          job_title: userData.job_title,
          privacy_settings: {
            show_email: false,
            show_mobile: false,
          },
        },
      });

      console.log(`🎓 Created alumni: ${user.email} (${tenant.name})`);
    }
  }

  // Create sample student users for each tenant
  console.log('👨‍🎓 Creating sample student users...');

  const sampleStudentsByTenant: Record<string, any[]> = {
    iitb: [
      {
        email: '<EMAIL>',
        full_name: 'Arjun Reddy',
        usn: 'CS2021001',
        batch_year: 2025,
        course_name: 'Computer Science and Engineering',
      },
      {
        email: '<EMAIL>',
        full_name: 'Kavya Nair',
        usn: 'EE2022002',
        batch_year: 2026,
        course_name: 'Electrical Engineering',
      },
    ],
    nitk: [
      {
        email: '<EMAIL>',
        full_name: 'Rohit Singh',
        usn: 'IT2021001',
        batch_year: 2025,
        course_name: 'Information Technology',
      },
    ],
    iimb: [
      {
        email: '<EMAIL>',
        full_name: 'Ananya Joshi',
        usn: 'MBA2023001',
        batch_year: 2025,
        course_name: 'Master of Business Administration',
      },
    ],
  };

  for (const tenant of createdTenants) {
    const studentUsers = sampleStudentsByTenant[tenant.subdomain] || [];

    for (const userData of studentUsers) {
      const password = await bcrypt.hash('StudentPass123!', 12);

      // Find the course for this user
      const course = await prisma.course.findFirst({
        where: {
          tenant_id: tenant.id,
          course_name: userData.course_name,
        },
      });

      const user = await prisma.user.upsert({
        where: {
          idx_tenant_email: {
            tenant_id: tenant.id,
            email: userData.email,
          },
        },
        update: {},
        create: {
          tenant_id: tenant.id,
          email: userData.email,
          password_hash: password,
          full_name: userData.full_name,
          usn: userData.usn,
          role: UserRole.STUDENT,
          account_status: UserStatus.APPROVED,
        },
      });

      // Create user profile
      await prisma.userProfile.upsert({
        where: {
          user_id: user.id,
        },
        update: {},
        create: {
          user_id: user.id,
          tenant_id: tenant.id,
          course_id: course?.id || null,
          batch_year: userData.batch_year,
          privacy_settings: {
            show_email: false,
            show_mobile: false,
          },
        },
      });

      console.log(`👨‍🎓 Created student: ${user.email} (${tenant.name})`);
    }
  }

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch(e => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
