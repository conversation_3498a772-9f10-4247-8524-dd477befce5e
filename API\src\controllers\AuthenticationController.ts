/// <reference path="../types/express.d.ts" />
import { NextFunction, Request, Response } from 'express';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { AuthUtils } from '../utils/auth';
import { AuthenticatedRequest } from '../types/express';
import {
  toAuthUserViewModel,
  toLoginResponseViewModel,
  toRefreshTokenResponseViewModel,
  toLogoutResponseViewModel,
} from '../viewmodels/authViewModel';
import { createSuccessResponse } from '../viewmodels/responseViewModel';

interface LoginRequest {
  email: string;
  password: string;
  tenant_id: number;
}

/**
 * User Login
 * Authenticates user and provides JWT tokens
 */
export const login = async (
  req: Request<{}, {}, LoginRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { email, password, tenant_id } = req.body;

    // Find user with tenant context
    const user = await prisma.user.findFirst({
      where: {
        email,
        tenant_id,
      },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
          },
        },
      },
    });

    if (!user) {
      throw createError('Invalid credentials', 401);
    }

    // Verify password
    const isValidPassword = await AuthUtils.comparePassword(
      password,
      user.password_hash
    );

    if (!isValidPassword) {
      throw createError('Invalid credentials', 401);
    }

    // Check account status
    if (user.account_status === 'PENDING') {
      throw createError('Account is pending approval', 403);
    }

    if (user.account_status === 'REJECTED') {
      throw createError('Account has been rejected', 403);
    }

    if (user.account_status === 'DEACTIVATED') {
      throw createError('Account has been deactivated', 403);
    }

    // Generate tokens
    const tokens = await AuthUtils.generateTokenPair(user);

    // Set HTTP-only cookies
    res.cookie('accessToken', tokens.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 15 * 60 * 1000, // 15 minutes
    });

    res.cookie('refreshToken', tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    });

    const response = toLoginResponseViewModel(user);
    res.json(response);
  } catch (error) {
    next(error);
  }
};

/**
 * User Logout
 * Invalidates tokens and clears cookies
 */
export const logout = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const refreshToken = req.cookies.refreshToken;

    if (refreshToken) {
      // Add refresh token to blacklist (implement this in AuthUtils if needed)
      // await AuthUtils.blacklistToken(refreshToken);
    }

    // Clear cookies
    res.clearCookie('accessToken');
    res.clearCookie('refreshToken');

    const response = toLogoutResponseViewModel();
    res.json(response);
  } catch (error) {
    next(error);
  }
};

/**
 * Refresh Token
 * Generates new access token using refresh token
 */
export const refreshToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const refreshToken = req.cookies.refreshToken || req.body.refreshToken;

    if (!refreshToken) {
      throw createError('Refresh token not provided', 401);
    }

    // Verify refresh token
    const decoded = await AuthUtils.verifyRefreshToken(refreshToken);

    // Check if token is blacklisted (implement this in AuthUtils if needed)
    // const isBlacklisted = await AuthUtils.isTokenBlacklisted(refreshToken);
    // if (isBlacklisted) {
    //   throw createError('Invalid refresh token', 401);
    // }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: parseInt(decoded.userId) },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
          },
        },
      },
    });

    if (!user || user.account_status !== 'APPROVED') {
      throw createError('User not found or not approved', 401);
    }

    // Generate new tokens
    const tokens = await AuthUtils.generateTokenPair(user);

    // Blacklist old refresh token (implement this in AuthUtils if needed)
    // await AuthUtils.blacklistToken(refreshToken);

    // Set new cookies
    res.cookie('accessToken', tokens.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 15 * 60 * 1000, // 15 minutes
    });

    res.cookie('refreshToken', tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60 * 1000,
    });

    const response = toRefreshTokenResponseViewModel();
    res.json(response);
  } catch (error) {
    next(error);
  }
};

/**
 * Get Current User
 * Returns current authenticated user information
 */
export const getCurrentUser = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = parseInt(req.user.id);

    if (!userId) {
      throw createError('User not authenticated', 401);
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
          },
        },
      },
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    const response = createSuccessResponse(toAuthUserViewModel(user));
    res.json(response);
  } catch (error) {
    next(error);
  }
};

/**
 * Verify Token
 * Validates access token and returns user info
 */
export const verifyToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token =
      req.cookies.accessToken || req.headers.authorization?.split(' ')[1];

    if (!token) {
      throw createError('No token provided', 401);
    }

    const decoded = await AuthUtils.verifyAccessToken(token);

    const user = await prisma.user.findUnique({
      where: { id: parseInt(decoded.userId) },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
          },
        },
      },
    });

    if (!user || user.account_status !== 'APPROVED') {
      throw createError('Invalid token or user not approved', 401);
    }

    const response = createSuccessResponse({
      valid: true,
      user: toAuthUserViewModel(user),
    });

    res.json(response);
  } catch (error) {
    next(error);
  }
};

/**
 * Change Password
 * Allows authenticated users to change their password
 */
export const changePassword = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = parseInt(req.user.id);

    if (!userId) {
      throw createError('User not authenticated', 401);
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    // Verify current password
    const isValidPassword = await AuthUtils.comparePassword(
      currentPassword,
      user.password_hash
    );

    if (!isValidPassword) {
      throw createError('Current password is incorrect', 400);
    }

    // Hash new password
    const hashedNewPassword = await AuthUtils.hashPassword(newPassword);

    // Update password
    await prisma.user.update({
      where: { id: userId },
      data: {
        password_hash: hashedNewPassword,
      },
    });

    res.json({
      success: true,
      message: 'Password changed successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Revoke All Sessions
 * Invalidates all refresh tokens for the user
 */
export const revokeAllSessions = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = parseInt(req.user.id);

    if (!userId) {
      throw createError('User not authenticated', 401);
    }

    // In a production system, you would invalidate all refresh tokens for this user
    // For now, we'll just clear the current session cookies
    res.clearCookie('accessToken');
    res.clearCookie('refreshToken');

    res.json({
      success: true,
      message: 'All sessions revoked successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};
