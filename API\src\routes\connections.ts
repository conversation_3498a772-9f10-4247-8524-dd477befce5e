import express from 'express';
import { body, param, query } from 'express-validator';
import { validateRequest } from '../middleware/validateRequest';
import { authenticateToken } from '../middleware/auth';
import * as ConnectionController from '../controllers/ConnectionController';

const router = express.Router();

/**
 * @swagger
 * /api/connections:
 *   get:
 *     summary: Get user connections
 *     tags: [Connections]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Connections retrieved successfully
 *       401:
 *         description: Not authenticated
 */
router.get(
  '/',
  authenticateToken,
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
  ],
  validateRequest,
  ConnectionController.getConnections
);

/**
 * @swagger
 * /api/connections/requests:
 *   get:
 *     summary: Get connection requests
 *     tags: [Connections]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [received, sent]
 *         description: Type of requests to retrieve
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Connection requests retrieved successfully
 *       401:
 *         description: Not authenticated
 */
router.get(
  '/requests',
  authenticateToken,
  [
    query('type').optional().isIn(['received', 'sent']),
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
  ],
  validateRequest,
  ConnectionController.getConnectionRequests
);

/**
 * @swagger
 * /api/connections/send:
 *   post:
 *     summary: Send connection request
 *     tags: [Connections]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - target_user_id
 *             properties:
 *               target_user_id:
 *                 type: integer
 *               message:
 *                 type: string
 *                 maxLength: 500
 *     responses:
 *       201:
 *         description: Connection request sent successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Not authenticated
 *       404:
 *         description: Target user not found
 *       409:
 *         description: Connection already exists
 */
router.post(
  '/send',
  authenticateToken,
  [
    body('target_user_id').isInt({ min: 1 }),
    body('message').optional().isLength({ max: 500 }).trim(),
  ],
  validateRequest,
  ConnectionController.sendConnectionRequest
);

/**
 * @swagger
 * /api/connections/{id}/respond:
 *   put:
 *     summary: Respond to connection request
 *     tags: [Connections]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Connection request ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [ACCEPTED, REJECTED]
 *     responses:
 *       200:
 *         description: Connection request responded to successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Not authenticated
 *       404:
 *         description: Connection request not found
 */
router.put(
  '/:id/respond',
  authenticateToken,
  [
    param('id').isInt({ min: 1 }),
    body('status').isIn(['ACCEPTED', 'REJECTED']),
  ],
  validateRequest,
  ConnectionController.respondToConnection
);

/**
 * @swagger
 * /api/connections/{id}:
 *   delete:
 *     summary: Remove connection
 *     tags: [Connections]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Connection ID
 *     responses:
 *       200:
 *         description: Connection removed successfully
 *       401:
 *         description: Not authenticated
 *       404:
 *         description: Connection not found
 */
router.delete(
  '/:id',
  authenticateToken,
  [param('id').isInt({ min: 1 })],
  validateRequest,
  ConnectionController.removeConnection
);

/**
 * @swagger
 * /api/connections/mutual/{userId}:
 *   get:
 *     summary: Get mutual connections with another user
 *     tags: [Connections]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Target user ID
 *     responses:
 *       200:
 *         description: Mutual connections retrieved successfully
 *       400:
 *         description: Invalid user ID
 *       401:
 *         description: Not authenticated
 */
router.get(
  '/mutual/:userId',
  authenticateToken,
  [param('userId').isInt({ min: 1 })],
  validateRequest,
  ConnectionController.getMutualConnections
);

export default router;
