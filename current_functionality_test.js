const http = require('http');

// Test results tracker
const results = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, passed, details = '') {
  results.tests.push({ name, passed, details });
  if (passed) {
    results.passed++;
    console.log(`✅ ${name}`);
    if (details) console.log(`   ${details}`);
  } else {
    results.failed++;
    console.log(`❌ ${name}: ${details}`);
  }
}

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: responseData
        });
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(data);
    }
    req.end();
  });
}

async function testCurrentFunctionality() {
  console.log('🎯 CURRENT FUNCTIONALITY TEST WITH VIEWMODELS');
  console.log('='.repeat(60));
  console.log('Testing existing functionality with enhanced ViewModels...\n');

  let cookies = null;

  try {
    // Test 1: Authentication with ViewModels
    console.log('🔐 SECTION 1: AUTHENTICATION WITH VIEWMODELS');
    console.log('-'.repeat(50));
    
    const loginData = JSON.stringify({
      email: '<EMAIL>',
      password: 'AdminPass123!',
      tenant_id: 8
    });

    const loginResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginData)
      }
    }, loginData);

    const loginResult = JSON.parse(loginResponse.data);
    cookies = loginResponse.headers['set-cookie'];
    
    logTest('Login with ViewModel response', 
      loginResponse.statusCode === 200 && loginResult.success && loginResult.user,
      `Status: ${loginResponse.statusCode}, Has user object: ${!!loginResult.user}`
    );

    logTest('User object excludes sensitive data', 
      !loginResult.user?.password_hash && !loginResult.user?.reset_token,
      'No password hash or reset token in response'
    );

    logTest('Response includes timestamp', 
      loginResult.timestamp,
      `Timestamp: ${loginResult.timestamp}`
    );

    // Test 2: User Profile with ViewModels
    console.log('\n👤 SECTION 2: USER PROFILE WITH VIEWMODELS');
    console.log('-'.repeat(50));
    
    if (cookies) {
      const profileResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/users/profile',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      const profileResult = JSON.parse(profileResponse.data);
      
      logTest('Profile response has ViewModel structure', 
        profileResult.success && profileResult.data,
        'Has success and data fields'
      );

      logTest('Profile includes tenant_id', 
        profileResult.data?.tenant_id === 8,
        `Tenant ID: ${profileResult.data?.tenant_id}`
      );

      logTest('Profile excludes sensitive internal fields', 
        !profileResult.data?.password_hash && !profileResult.data?.email_verification_token,
        'No internal sensitive fields exposed'
      );
    }

    // Test 3: Admin Dashboard with ViewModels
    console.log('\n👑 SECTION 3: ADMIN DASHBOARD WITH VIEWMODELS');
    console.log('-'.repeat(50));
    
    if (cookies) {
      const dashboardResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/admin/dashboard',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      const dashboardResult = JSON.parse(dashboardResponse.data);
      
      logTest('Dashboard response has ViewModel structure', 
        dashboardResult.success && dashboardResult.data,
        'Has success and data fields'
      );

      logTest('Dashboard includes comprehensive stats', 
        dashboardResult.data?.users && dashboardResult.data?.content,
        `Users stats: ${!!dashboardResult.data?.users}, Content stats: ${!!dashboardResult.data?.content}`
      );
    }

    // Test 4: Admin Users with ViewModels
    console.log('\n📊 SECTION 4: ADMIN USERS WITH VIEWMODELS');
    console.log('-'.repeat(50));
    
    if (cookies) {
      const adminUsersResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/admin/users?page=1&limit=5',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      const adminUsersResult = JSON.parse(adminUsersResponse.data);
      
      logTest('Admin users response has paginated ViewModel', 
        adminUsersResult.success && adminUsersResult.data && adminUsersResult.pagination,
        'Has success, data, and pagination fields'
      );

      logTest('Pagination includes all required fields', 
        adminUsersResult.pagination?.page && adminUsersResult.pagination?.total !== undefined,
        `Page: ${adminUsersResult.pagination?.page}, Total: ${adminUsersResult.pagination?.total}`
      );

      logTest('User objects use admin ViewModel', 
        adminUsersResult.data?.length > 0 && adminUsersResult.data[0]?.id,
        `Users returned: ${adminUsersResult.data?.length || 0}`
      );
    }

    // Test 5: User Directory with ViewModels
    console.log('\n📖 SECTION 5: USER DIRECTORY WITH VIEWMODELS');
    console.log('-'.repeat(50));
    
    if (cookies) {
      const directoryResponse = await makeRequest({
        hostname: 'localhost',
        port: 5000,
        path: '/api/users/directory?page=1&limit=5',
        method: 'GET',
        headers: {
          'Cookie': cookies.join('; ')
        }
      });

      const directoryResult = JSON.parse(directoryResponse.data);
      
      logTest('Directory response has paginated ViewModel', 
        directoryResult.success && directoryResult.data && directoryResult.pagination,
        'Has success, data, and pagination fields'
      );

      logTest('Directory users have privacy filtering', 
        directoryResult.data?.length > 0,
        `Users in directory: ${directoryResult.data?.length || 0}`
      );
    }

    // Test 6: Error Response ViewModels
    console.log('\n⚠️ SECTION 6: ERROR RESPONSE VIEWMODELS');
    console.log('-'.repeat(50));
    
    // Test 401 Unauthorized
    const unauthorizedResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api/admin/users',
      method: 'GET'
    });

    const unauthorizedResult = JSON.parse(unauthorizedResponse.data);
    
    logTest('401 error has proper ViewModel structure', 
      unauthorizedResponse.statusCode === 401 && unauthorizedResult.error && unauthorizedResult.timestamp,
      `Status: ${unauthorizedResponse.statusCode}, Has error and timestamp`
    );

    logTest('Error response includes path and method', 
      unauthorizedResult.path && unauthorizedResult.method,
      `Path: ${unauthorizedResult.path}, Method: ${unauthorizedResult.method}`
    );

    // Test 7: Health Check
    console.log('\n🏥 SECTION 7: HEALTH CHECK');
    console.log('-'.repeat(50));
    
    const healthResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/health',
      method: 'GET'
    });

    logTest('Health check endpoint', 
      healthResponse.statusCode === 200,
      `Status: ${healthResponse.statusCode}`
    );

  } catch (error) {
    console.error('❌ Test execution error:', error.message);
    logTest('Test execution', false, error.message);
  }

  // Print comprehensive summary
  console.log('\n' + '='.repeat(60));
  console.log('🎯 CURRENT FUNCTIONALITY TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 ALL CURRENT FUNCTIONALITY TESTS PASSED!');
    console.log('✨ ViewModels and enhanced functionality working perfectly.');
    console.log('\n🔥 CURRENT ACHIEVEMENTS:');
    console.log('   ✅ Authentication ViewModels filter sensitive data');
    console.log('   ✅ User profile ViewModels include required fields');
    console.log('   ✅ Admin ViewModels provide comprehensive data');
    console.log('   ✅ Error responses have consistent structure');
    console.log('   ✅ All responses include proper timestamps');
    console.log('   ✅ Pagination ViewModels work correctly');
    console.log('   ✅ Privacy filtering implemented');
  } else {
    console.log('\n⚠️  Some functionality tests failed. Review details above.');
  }
  
  console.log('\n📋 Next Steps for Controller Organization:');
  console.log('   • Complete implementation of new controller structure');
  console.log('   • Fix TypeScript compilation issues');
  console.log('   • Implement proper AuthenticatedRequest type');
  console.log('   • Add comprehensive error handling');
  console.log('   • Test new organized endpoints');
}

testCurrentFunctionality();
